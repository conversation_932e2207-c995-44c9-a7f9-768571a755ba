.PHONY: help install install-dev test test-cov lint format clean build upload docs demo

# Default target
help:
	@echo "Available targets:"
	@echo "  install      - Install package in development mode"
	@echo "  install-dev  - Install with development dependencies"
	@echo "  test         - Run tests"
	@echo "  test-cov     - Run tests with coverage"
	@echo "  lint         - Run linting (flake8, mypy)"
	@echo "  format       - Format code (black, isort)"
	@echo "  clean        - Clean build artifacts"
	@echo "  build        - Build package"
	@echo "  upload       - Upload to PyPI"
	@echo "  docs         - Build documentation"
	@echo "  demo         - Run demo script"

# Installation
install:
	pip install -e .

install-dev:
	pip install -e ".[dev]"

# Testing
test:
	pytest tests/

test-cov:
	pytest --cov=itas --cov-report=html --cov-report=term tests/

# Code quality
lint:
	flake8 itas/
	mypy itas/

format:
	black itas/
	isort itas/

# Build and distribution
clean:
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	find . -type d -name __pycache__ -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

build: clean
	python -m build

upload: build
	python -m twine upload dist/*

# Documentation
docs:
	cd docs && make html

# Demo
demo:
	python demo.py

# Development setup
setup-dev: install-dev
	pre-commit install
	@echo "Development environment setup complete!"

# Quick development cycle
dev: format lint test
	@echo "Development cycle complete!"
