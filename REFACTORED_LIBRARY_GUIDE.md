# ITAS Refactored Library Guide

## 🚀 Overview

This document describes the comprehensive refactoring of the ITAS (Instruction-Truth Activation Steering) library to support any HuggingFace model and dataset, following software engineering best practices.

## ✨ Key Improvements

### 1. Universal Model Support
- **Before**: Limited to LLaMA and Gemma models with hardcoded configurations
- **After**: Works with any HuggingFace transformer model (GPT, BERT, T5, OPT, etc.)
- **Implementation**: `UniversalModelLoader` with automatic architecture detection

### 2. Flexible Dataset Handling
- **Before**: Hardcoded dataset paths and preprocessing
- **After**: Support for any HuggingFace dataset with configurable preprocessing
- **Implementation**: `DatasetManager` with streaming and chunking support

### 3. Multiple SAE Architectures
- **Before**: Single SAE implementation
- **After**: Standard, Gated, and JumpReLU SAE variants
- **Implementation**: Unified `SAE` class with architecture parameter

### 4. Comprehensive Configuration
- **Before**: Scattered configuration across multiple files
- **After**: Hierarchical configuration system with validation
- **Implementation**: `SAEConfig` with nested configuration classes

### 5. Professional Software Engineering
- **Before**: Limited error handling and documentation
- **After**: Type hints, logging, error handling, and comprehensive documentation
- **Implementation**: Following Python best practices throughout

## 🏗️ New Architecture

### Core Module (`itas.core`)

```python
# Configuration Management
from itas.core.config import SAEConfig, ModelConfig, DatasetConfig, TrainingConfig

# Model Loading
from itas.core.model_loader import UniversalModelLoader

# Dataset Handling
from itas.core.dataset_manager import DatasetManager

# SAE Implementation
from itas.core.sae import SAE, TrainingSAE

# Training
from itas.core.trainer import SAETrainer

# Activation Management
from itas.core.activations_store import ActivationsStore
```

### Analysis Module (`itas.analysis`)

```python
# Function Extraction
from itas.analysis.function_extractor import FunctionExtractor

# Representation Engineering
from itas.analysis.representation_engineer import RepresentationEngineer

# Activation Analysis
from itas.analysis.activation_analyzer import ActivationAnalyzer

# Evaluation
from itas.analysis.evaluation import SAEEvaluator

# Visualization
from itas.analysis.visualization import SAEVisualizer
```

## 🔧 Usage Examples

### 1. Basic SAE Training

```python
import itas

# Create configuration for any HuggingFace model
config = itas.SAEConfig(
    model=itas.ModelConfig(
        model_name="microsoft/DialoGPT-medium",  # Any HF model
        use_flash_attention=True,
        torch_dtype="bfloat16"
    ),
    dataset=itas.DatasetConfig(
        dataset_name="wikitext",  # Any HF dataset
        text_column="text"
    ),
    training=itas.TrainingConfig(
        total_training_tokens=1_000_000,
        batch_size=2048,
        l1_coefficient=1e-3
    ),
    hook_layer=6,
    expansion_factor=32,
    architecture="standard"
)

# Train SAE
trainer = itas.SAETrainer(config)
sae = trainer.train()
```

### 2. Universal Model Loading

```python
# Load any HuggingFace model
model, tokenizer = itas.load_model_and_tokenizer("facebook/opt-1.3b")

# Get model information
model_loader = itas.UniversalModelLoader(config.model)
model_info = model_loader.get_model_info()
hook_names = model_loader.get_hook_names()
```

### 3. Flexible Dataset Processing

```python
# Use any HuggingFace dataset
dataset_config = spare.DatasetConfig(
    dataset_name="your-custom-dataset",
    text_column="your_text_column",
    streaming=True  # For large datasets
)

dataset_manager = spare.DatasetManager(dataset_config, tokenizer)
dataset = dataset_manager.load_dataset()
processed = dataset_manager.preprocess_dataset()
```

### 4. Advanced Function Extraction

```python
# Create function extractor
function_extractor = spare.FunctionExtractor(sae)

# Extract behavioral function
result = function_extractor.extract_function(
    target_activations=positive_examples,
    context_activations=negative_examples,
    learning_rate=1e-3,
    num_iterations=1000
)

# Analyze results
importance_stats = function_extractor.analyze_feature_importance()
top_features = function_extractor.get_top_features(k=20)
```

### 5. Representation Engineering

```python
# Create representation engineer
engineer = spare.RepresentationEngineer(
    model=model,
    tokenizer=tokenizer,
    sae=sae,
    hook_layer=config.hook_layer,
    hook_name=config.hook_name
)

# Create steering vector
steering_vector = engineer.create_steering_vector(
    positive_examples=["Context-based answers..."],
    negative_examples=["Memory-based answers..."]
)

# Apply intervention
intervention_fn = engineer.apply_steering_intervention(steering_vector, strength=1.0)
result = engineer.test_intervention(test_texts, intervention_fn)
```

### 6. Comprehensive Evaluation

```python
# Evaluate SAE quality
evaluator = spare.SAEEvaluator()
evaluation = evaluator.evaluate_sae_comprehensive(sae, test_activations)

print(f"Overall SAE score: {evaluation.overall_score:.4f}")
print(f"Reconstruction FVU: {evaluation.reconstruction_metrics['fvu']:.4f}")
print(f"Sparsity: {evaluation.sparsity_metrics['overall_sparsity']:.4f}")

# Compare multiple SAEs
comparison = evaluator.compare_saes([sae1, sae2, sae3], test_data)
```

### 7. Visualization

```python
# Create visualizations
visualizer = spare.SAEVisualizer()

# Plot activation distributions
fig1 = visualizer.plot_activation_distribution(activations)

# Plot feature importance
fig2 = visualizer.plot_feature_importance(importance_scores)

# Plot evaluation metrics
fig3 = visualizer.plot_evaluation_metrics(evaluation_results)

# Plot SAE comparison
fig4 = visualizer.plot_sae_comparison(comparison_results)
```

## 🔄 Migration Guide

### From Legacy Code

The refactored library maintains backward compatibility:

```python
# Legacy (still works but deprecated)
model, tokenizer = spare.load_model(model_path, flash_attn=True)
sae = spare.load_frozen_sae(layer_idx, model_name)

# New recommended approach
model, tokenizer = spare.load_model_and_tokenizer(
    model_name=model_path,
    use_flash_attention=True
)

config = spare.SAEConfig(model_name=model_path, hook_layer=layer_idx)
trainer = spare.SAETrainer(config)
sae = trainer.train()
```

### Configuration Migration

```python
# Old: Scattered configuration
model_path = "meta-llama/Llama-2-7b-hf"
layer_ids = [12, 13, 14, 15]
batch_size = 4096
l1_coeff = 1e-3

# New: Unified configuration
config = spare.SAEConfig(
    model=spare.ModelConfig(model_name="meta-llama/Llama-2-7b-hf"),
    training=spare.TrainingConfig(
        batch_size=4096,
        l1_coefficient=1e-3
    ),
    hook_layer=12
)
```

## 📊 Supported Models and Datasets

### Models
- **GPT Family**: GPT-2, GPT-3.5, DialoGPT, CodeGPT
- **LLaMA Family**: LLaMA, LLaMA-2, Code Llama, Vicuna, Alpaca
- **Gemma Family**: Gemma, Gemma-2
- **BERT Family**: BERT, RoBERTa, DistilBERT, ELECTRA
- **T5 Family**: T5, FLAN-T5, UL2
- **OPT Family**: OPT models (125M to 175B)
- **And many more HuggingFace models...**

### Datasets
- **Text Datasets**: WikiText, OpenWebText, C4, BookCorpus
- **QA Datasets**: SQuAD, Natural Questions, MS MARCO
- **Custom Datasets**: Any HuggingFace dataset with text column
- **Streaming Support**: For datasets too large to fit in memory

## 🎯 Benefits

1. **Generalizability**: Works with any transformer architecture
2. **Flexibility**: Configurable for different use cases
3. **Scalability**: Handles large models and datasets efficiently
4. **Maintainability**: Clean, modular code with proper documentation
5. **Extensibility**: Easy to add new SAE architectures and features
6. **Reliability**: Comprehensive error handling and validation
7. **Usability**: Simple API with sensible defaults

## 🚀 Getting Started

1. **Install the library**:
   ```bash
   pip install -e .
   ```

2. **Run the demo**:
   ```bash
   python demo.py
   ```

3. **Try different models**:
   ```python
   # Test with different architectures
   models = ["microsoft/DialoGPT-medium", "distilbert-base-uncased", "facebook/opt-125m"]
   for model_name in models:
       config = spare.SAEConfig(model_name=model_name)
       # ... train SAE
   ```

4. **Explore the documentation**:
   - Check docstrings in each module
   - Review the demo.py for comprehensive examples
   - Look at the configuration options in `spare.core.config`

## 📝 Next Steps

The refactored library provides a solid foundation for:
- Adding new SAE architectures
- Supporting additional model types
- Implementing new intervention techniques
- Scaling to larger models and datasets
- Building applications on top of the library

This refactoring transforms SpARE from a research prototype into a production-ready library suitable for widespread use in the representation engineering community.
