# Install SpARE if not already installed
# !pip install -e .

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from transformers import AutoTokenizer
import logging

# SpARE imports
import spare
from spare import (
    SAEConfig, ModelConfig, DatasetConfig, TrainingConfig,
    SAETrainer, FunctionExtractor, RepresentationEngineer,
    SAEEvaluator, SAEVisualizer, UniversalModelLoader,
    load_model_and_tokenizer, create_sae_config
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Check GPU availability
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Using device: {device}")
if device == "cuda":
    print(f"GPU: {torch.cuda.get_device_name()}")
    print(f"VRAM: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

# Model configuration
model_name = "meta-llama/Llama-3.1-8B-Instruct"

# Note: You need HuggingFace access to LLaMA models
# Make sure you're logged in: huggingface-cli login

print(f"Loading {model_name}...")

# Load model and tokenizer with optimizations
model, tokenizer = load_model_and_tokenizer(
    model_name=model_name,
    use_flash_attention=True,  # Automatic compatibility detection
    torch_dtype="bfloat16",    # Memory efficient
    device_map="auto",         # Automatic device placement
    trust_remote_code=False
)

print(f"✓ Model loaded successfully!")
print(f"Model device: {next(model.parameters()).device}")
print(f"Model dtype: {next(model.parameters()).dtype}")

# Get detailed model information
model_loader = UniversalModelLoader(
    ModelConfig(model_name=model_name)
)

model_info = model_loader.get_model_info()
hook_names = model_loader.get_hook_names()

print("📊 Model Information:")
print(f"  Model: {model_info['model_name']}")
print(f"  Architecture: {model_info['architecture']}")
print(f"  Hidden size: {model_info['hidden_size']}")
print(f"  Number of layers: {model_info['num_layers']}")
print(f"  Total parameters: {model_info['total_parameters']:,}")
print(f"  Vocabulary size: {model_info['vocab_size']:,}")

print("\n🔗 Available Hook Points:")
for hook_type, hooks in list(hook_names.items())[:3]:  # Show first 3 types
    print(f"  {hook_type}: {len(hooks)} hooks")
    if hooks:
        print(f"    Example: {hooks[0]}")

# Choose a middle layer for SAE training (good balance of complexity and interpretability)
target_layer = model_info['num_layers'] // 2
print(f"\n🎯 Target layer for SAE training: {target_layer}")

# Dataset configuration
dataset_config = DatasetConfig(
    dataset_name="wikitext",
    dataset_kwargs={"name": "wikitext-2-raw-v1"},  # Specify WikiText variant
    dataset_split="train",
    text_column="text",
    max_seq_length=2048,  # LLaMA 3.1 context length
    chunk_size=2048,
    streaming=False,      # Load full dataset for tutorial
    num_proc=4,          # Parallel processing
    trust_remote_code=False,
)

print("📚 Loading and preprocessing dataset...")

# Initialize dataset manager
from spare.data.dataset_manager import DatasetManager
dataset_manager = DatasetManager(dataset_config, tokenizer)

# Load and preprocess dataset
dataset = dataset_manager.load_dataset()
processed_dataset = dataset_manager.preprocess_dataset()

# Get dataset statistics
dataset_info = dataset_manager.get_dataset_info()
print(f"✓ Dataset loaded successfully!")
print(f"  Dataset: {dataset_info['dataset_name']}")
print(f"  Raw size: {dataset_info['raw_size']:,} examples")
print(f"  Processed size: {dataset_info['processed_size']:,} examples")
print(f"  Total tokens: {dataset_info.get('total_tokens', 'Unknown'):,}")

# Show a sample
sample = processed_dataset[0]
print(f"\n📝 Sample text (first 200 chars):")
print(f"'{tokenizer.decode(sample['input_ids'][:50])}...'")

# Create comprehensive SAE configuration
config = SAEConfig(
    # Model configuration
    model=ModelConfig(
        model_name=model_name,
        use_flash_attention=True,
        torch_dtype="bfloat16",
        trust_remote_code=False,
        device_map="auto",
    ),
    
    # Dataset configuration
    dataset=dataset_config,
    
    # Training configuration
    training=TrainingConfig(
        total_training_tokens=50_000_000,  # 50M tokens for tutorial
        batch_size=4096,                   # Adjust based on your GPU
        learning_rate=3e-4,
        l1_coefficient=1e-3,               # Sparsity regularization
        lr_scheduler="cosine",
        lr_warm_up_steps=1000,
        
        # Checkpointing and logging
        checkpoint_every_n_tokens=10_000_000,
        save_checkpoint_dir="./checkpoints",
        log_every_n_steps=100,
        eval_every_n_tokens=5_000_000,
        
        # Weights & Biases logging (optional)
        use_wandb=False,  # Set to True if you want W&B logging
        wandb_project="spare-llama-tutorial",
    ),
    
    # SAE architecture
    architecture="gated",              # Start with gated SAE (best performance)
    expansion_factor=32,               # 32x expansion (4096 -> 131,072 features)
    hook_layer=target_layer,           # Middle layer
    hook_name=f"model.layers.{target_layer}.mlp",  # Hook into MLP
    activation_fn="relu",
    normalize_decoder=True,
    
    # Device and precision
    device=device,
    dtype="float32",                   # Training precision
    seed=42,
)

print("⚙️ SAE Configuration:")
print(f"  Architecture: {config.architecture}")
print(f"  Hook layer: {config.hook_layer}")
print(f"  Hook name: {config.hook_name}")
print(f"  Expansion factor: {config.expansion_factor}")
print(f"  Hidden size: {model_info['hidden_size']}")
print(f"  SAE features: {model_info['hidden_size'] * config.expansion_factor:,}")
print(f"  Training tokens: {config.training.total_training_tokens:,}")
print(f"  Batch size: {config.training.batch_size}")

# Validate configuration
try:
    config.validate()
    print("✓ Configuration is valid!")
except Exception as e:
    print(f"❌ Configuration error: {e}")
    raise

# Create SAE trainer
print("🚀 Initializing SAE trainer...")
trainer = SAETrainer(config)

# Start training
print("🎯 Starting SAE training...")
print("This may take 30-60 minutes depending on your hardware.")
print("You can monitor progress in the logs.")

# Train the SAE
sae = trainer.train()

print("✅ SAE training completed!")
print(f"Final training metrics:")
metrics = trainer.get_training_metrics()
if metrics:
    print(f"  Final loss: {metrics.get('final_loss', 'N/A'):.6f}")
    print(f"  Final sparsity: {metrics.get('final_sparsity', 'N/A'):.4f}")
    print(f"  Final FVU: {metrics.get('final_fvu', 'N/A'):.4f}")

# Save the trained SAE
save_path = f"llama_3_1_8b_layer{target_layer}_gated_sae.pt"
sae.save(save_path)
print(f"💾 SAE saved to: {save_path}")

# Quick evaluation of the trained SAE
print("📊 Evaluating trained SAE...")

# Get some test activations
test_texts = [
    "The capital of France is Paris.",
    "Machine learning is a subset of artificial intelligence.",
    "The mitochondria is the powerhouse of the cell.",
    "Shakespeare wrote Romeo and Juliet.",
    "Python is a popular programming language."
]

# Tokenize test texts
test_inputs = tokenizer(test_texts, return_tensors="pt", padding=True, truncation=True)
test_inputs = {k: v.to(device) for k, v in test_inputs.items()}

# Get activations from the model
with torch.no_grad():
    outputs = model(**test_inputs, output_hidden_states=True)
    # Extract activations from our target layer
    test_activations = outputs.hidden_states[target_layer]  # Shape: [batch, seq_len, hidden_size]
    # Flatten to [batch * seq_len, hidden_size]
    test_activations = test_activations.view(-1, test_activations.size(-1))

print(f"Test activations shape: {test_activations.shape}")

# Test SAE reconstruction
with torch.no_grad():
    sae_output = sae(test_activations)
    reconstructed = sae_output.reconstructed
    feature_activations = sae_output.feature_activations

# Calculate basic metrics
mse = torch.mean((test_activations - reconstructed) ** 2).item()
cosine_sim = torch.nn.functional.cosine_similarity(
    test_activations.flatten(), reconstructed.flatten(), dim=0
).item()
sparsity = (feature_activations == 0).float().mean().item()

print(f"\n📈 Quick Evaluation Results:")
print(f"  MSE: {mse:.6f}")
print(f"  Cosine Similarity: {cosine_sim:.4f}")
print(f"  Sparsity: {sparsity:.4f}")
print(f"  Active Features: {(feature_activations > 0).sum(dim=1).float().mean():.1f} / {feature_activations.size(1)}")

# Train different SAE architectures for comparison
architectures = ["standard", "gated", "jumprelu"]
trained_saes = {}
evaluation_results = {}

for arch in architectures:
    print(f"\n🏗️ Training {arch.upper()} SAE...")
    
    # Create config for this architecture
    arch_config = config.copy()
    arch_config.architecture = arch
    # Use fewer tokens for comparison (faster training)
    arch_config.training.total_training_tokens = 10_000_000  # 10M tokens
    
    # Train SAE
    trainer = SAETrainer(arch_config)
    sae = trainer.train()
    
    # Save SAE
    save_path = f"llama_3_1_8b_layer{target_layer}_{arch}_sae.pt"
    sae.save(save_path)
    trained_saes[arch] = sae
    
    # Quick evaluation
    with torch.no_grad():
        sae_output = sae(test_activations)
        reconstructed = sae_output.reconstructed
        feature_activations = sae_output.feature_activations
    
    mse = torch.mean((test_activations - reconstructed) ** 2).item()
    cosine_sim = torch.nn.functional.cosine_similarity(
        test_activations.flatten(), reconstructed.flatten(), dim=0
    ).item()
    sparsity = (feature_activations == 0).float().mean().item()
    
    evaluation_results[arch] = {
        'mse': mse,
        'cosine_sim': cosine_sim,
        'sparsity': sparsity,
        'active_features': (feature_activations > 0).sum(dim=1).float().mean().item()
    }
    
    print(f"  ✓ {arch.upper()} SAE completed")
    print(f"    MSE: {mse:.6f}, Cosine Sim: {cosine_sim:.4f}, Sparsity: {sparsity:.4f}")

print("\n🏆 Architecture Comparison Results:")
print("Architecture | MSE      | Cosine Sim | Sparsity | Active Features")
print("-" * 65)
for arch, results in evaluation_results.items():
    print(f"{arch:11} | {results['mse']:.6f} | {results['cosine_sim']:.6f} | {results['sparsity']:.6f} | {results['active_features']:.1f}")

# Function extraction: Context vs Knowledge behavior
print("🔍 Starting function extraction...")

# Use the best performing SAE (typically gated)
best_sae = trained_saes['gated']

# Create function extractor
function_extractor = FunctionExtractor(
    sae=best_sae,
    initialization_method="uniform",
    regularization_strength=1e-5,
    device=device
)

# Define examples for different behaviors
context_based_prompts = [
    "Based on the context provided, the answer is clear.",
    "According to the given information, we can conclude that.",
    "The context clearly states that the solution is.",
    "From the provided text, it's evident that.",
    "The passage indicates that the correct answer is."
]

knowledge_based_prompts = [
    "From my knowledge, I believe the answer is.",
    "Based on what I know, the solution should be.",
    "Generally speaking, this type of problem requires.",
    "In my understanding, the correct approach is.",
    "From general knowledge, we can determine that."
]

# Get activations for both types of prompts
def get_activations_for_prompts(prompts):
    inputs = tokenizer(prompts, return_tensors="pt", padding=True, truncation=True)
    inputs = {k: v.to(device) for k, v in inputs.items()}
    
    with torch.no_grad():
        outputs = model(**inputs, output_hidden_states=True)
        activations = outputs.hidden_states[target_layer]
        # Take the last token activation for each sequence
        activations = activations[:, -1, :]  # [batch_size, hidden_size]
    
    return activations

context_activations = get_activations_for_prompts(context_based_prompts)
knowledge_activations = get_activations_for_prompts(knowledge_based_prompts)

print(f"Context activations shape: {context_activations.shape}")
print(f"Knowledge activations shape: {knowledge_activations.shape}")

# Extract function
print("\n🎯 Extracting behavioral function...")
extraction_result = function_extractor.extract_function(
    target_activations=context_activations,
    context_activations=knowledge_activations,
    learning_rate=1e-3,
    num_iterations=1000,
    verbose=True
)

print(f"\n✅ Function extraction completed!")
print(f"  Active features: {len(extraction_result.active_features)}")
print(f"  Extraction strength: {extraction_result.extraction_strength:.6f}")
print(f"  Final loss: {extraction_result.metadata.get('final_loss', 'N/A'):.6f}")

# Analyze feature importance
importance_stats = function_extractor.analyze_feature_importance()
print(f"\n📊 Feature Importance Statistics:")
print(f"  Mean importance: {importance_stats['mean']:.6f}")
print(f"  Std importance: {importance_stats['std']:.6f}")
print(f"  Max importance: {importance_stats['max']:.6f}")

# Get top features
top_features = function_extractor.get_top_features(k=20)
print(f"\n🔝 Top 20 Features: {top_features[:10]}...")

# Create representation engineer
print("🎛️ Setting up representation engineering...")

engineer = RepresentationEngineer(
    model=model,
    tokenizer=tokenizer,
    sae=best_sae,
    hook_layer=target_layer,
    hook_name=f"model.layers.{target_layer}.mlp"
)

# Create steering vector using our extracted function
print("🧭 Creating steering vector...")
steering_vector = engineer.create_steering_vector(
    positive_examples=context_based_prompts,
    negative_examples=knowledge_based_prompts,
    strength=2.0
)

print(f"✓ Steering vector created with shape: {steering_vector.shape}")

# Test intervention on some prompts
test_prompts = [
    "What is the capital of France?",
    "How does photosynthesis work?",
    "Explain machine learning in simple terms.",
    "What are the benefits of renewable energy?"
]

print("\n🧪 Testing intervention effects...")

# Apply intervention
intervention_fn = engineer.apply_steering_intervention(
    steering_vector,
    strength=1.5,
    layers=[target_layer]
)

# Test intervention effectiveness
results = engineer.test_intervention(
    test_prompts,
    intervention_fn,
    max_new_tokens=50,
    temperature=0.7
)

print("\n📝 Intervention Results:")
print("=" * 80)
for i, (prompt, result) in enumerate(zip(test_prompts, results)):
    print(f"\nPrompt {i+1}: {prompt}")
    print(f"Original:  {result['original_text'][:100]}...")
    print(f"Modified:  {result['modified_text'][:100]}...")
    print("-" * 40)

# Comprehensive SAE evaluation
print("📊 Starting comprehensive SAE evaluation...")

# Create evaluator
evaluator = SAEEvaluator()

# Prepare larger test dataset for evaluation
eval_texts = [
    "The theory of relativity was developed by Einstein.",
    "Machine learning algorithms can learn from data.",
    "The human brain contains billions of neurons.",
    "Climate change affects global weather patterns.",
    "Quantum computers use quantum mechanical phenomena.",
    "DNA contains the genetic instructions for life.",
    "The internet connects computers worldwide.",
    "Renewable energy sources include solar and wind.",
    "Artificial intelligence mimics human intelligence.",
    "The periodic table organizes chemical elements."
]

# Get evaluation activations
eval_inputs = tokenizer(eval_texts, return_tensors="pt", padding=True, truncation=True)
eval_inputs = {k: v.to(device) for k, v in eval_inputs.items()}

with torch.no_grad():
    eval_outputs = model(**eval_inputs, output_hidden_states=True)
    eval_activations = eval_outputs.hidden_states[target_layer]
    eval_activations = eval_activations.view(-1, eval_activations.size(-1))

print(f"Evaluation activations shape: {eval_activations.shape}")

# Comprehensive evaluation for each SAE
comprehensive_results = {}

for arch_name, sae in trained_saes.items():
    print(f"\n🔍 Evaluating {arch_name.upper()} SAE...")
    
    evaluation = evaluator.evaluate_sae_comprehensive(
        sae=sae,
        test_activations=eval_activations,
        compute_feature_metrics=True,
        compute_reconstruction_metrics=True,
        compute_sparsity_metrics=True
    )
    
    comprehensive_results[arch_name] = evaluation
    
    print(f"  Overall Score: {evaluation.overall_score:.4f}")
    print(f"  Reconstruction FVU: {evaluation.reconstruction_metrics['fvu']:.4f}")
    print(f"  Sparsity: {evaluation.sparsity_metrics['overall_sparsity']:.4f}")
    print(f"  Active Features: {evaluation.sparsity_metrics['active_features']}")
    print(f"  Dead Features: {evaluation.sparsity_metrics['dead_features']}")

# Compare all SAEs
print("\n🏆 Final SAE Comparison:")
print("=" * 80)
print(f"{'Architecture':<12} {'Overall Score':<14} {'FVU':<8} {'Sparsity':<10} {'Active Features':<15}")
print("-" * 80)

for arch_name, evaluation in comprehensive_results.items():
    print(f"{arch_name:<12} {evaluation.overall_score:<14.4f} "
          f"{evaluation.reconstruction_metrics['fvu']:<8.4f} "
          f"{evaluation.sparsity_metrics['overall_sparsity']:<10.4f} "
          f"{evaluation.sparsity_metrics['active_features']:<15}")

# Find best performing SAE
best_arch = max(comprehensive_results.keys(), 
                key=lambda x: comprehensive_results[x].overall_score)
print(f"\n🥇 Best performing architecture: {best_arch.upper()}")
print(f"   Score: {comprehensive_results[best_arch].overall_score:.4f}")

# Create visualizations
print("📈 Creating visualizations...")

# Initialize visualizer
visualizer = SAEVisualizer()

# Set up matplotlib for better plots
plt.style.use('default')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

# 1. Plot activation distributions
fig1 = visualizer.plot_activation_distribution(
    activations=eval_activations.cpu().numpy(),
    title=f"LLaMA 3.1 8B Layer {target_layer} Activation Distribution",
    save_path="activation_distribution.png"
)
plt.show()

# 2. Plot feature importance from function extraction
if 'extraction_result' in locals():
    fig2 = visualizer.plot_feature_importance(
        importance_scores=extraction_result.feature_weights.cpu().numpy(),
        top_k=50,
        title="Top 50 Features: Context vs Knowledge Behavior",
        save_path="feature_importance.png"
    )
    plt.show()

# 3. Plot SAE architecture comparison
comparison_data = {
    arch: {
        'overall_score': results.overall_score,
        'fvu': results.reconstruction_metrics['fvu'],
        'sparsity': results.sparsity_metrics['overall_sparsity']
    }
    for arch, results in comprehensive_results.items()
}

fig3 = visualizer.plot_sae_comparison(
    comparison_results=comparison_data,
    metrics=["overall_score", "fvu", "sparsity"],
    title="SAE Architecture Comparison on LLaMA 3.1 8B",
    save_path="sae_comparison.png"
)
plt.show()

# 4. Create feature activation heatmap for best SAE
best_sae_obj = trained_saes[best_arch]
with torch.no_grad():
    sample_output = best_sae_obj(eval_activations[:10])  # First 10 samples
    feature_acts = sample_output.feature_activations.cpu().numpy()

# Plot heatmap of top active features
top_features_mask = np.sum(feature_acts > 0, axis=0) > 0  # Features active in at least one sample
active_features = feature_acts[:, top_features_mask]

if active_features.shape[1] > 0:
    plt.figure(figsize=(15, 8))
    plt.imshow(active_features[:, :min(100, active_features.shape[1])].T, 
               aspect='auto', cmap='viridis', interpolation='nearest')
    plt.colorbar(label='Feature Activation')
    plt.title(f'Feature Activation Heatmap - {best_arch.upper()} SAE')
    plt.xlabel('Sample Index')
    plt.ylabel('Feature Index')
    plt.tight_layout()
    plt.savefig('feature_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()

print("✅ All visualizations created and saved!")
print("📁 Saved files:")
print("  - activation_distribution.png")
print("  - feature_importance.png")
print("  - sae_comparison.png")
print("  - feature_heatmap.png")

# Tutorial summary
print("🎉 Tutorial Complete! Here's what we accomplished:")
print("=" * 60)

print("\n✅ 1. Model Setup:")
print(f"   - Loaded LLaMA 3.1 8B Instruct ({model_info['total_parameters']:,} parameters)")
print(f"   - Explored model architecture and hook points")
print(f"   - Selected layer {target_layer} for SAE training")

print("\n✅ 2. Dataset Preparation:")
print(f"   - Processed WikiText-2 dataset")
print(f"   - Tokenized and chunked text data")
print(f"   - Prepared {dataset_info.get('processed_size', 'N/A')} training examples")

print("\n✅ 3. SAE Training:")
print(f"   - Trained 3 different SAE architectures: Standard, Gated, JumpReLU")
print(f"   - Used {config.expansion_factor}x expansion factor")
print(f"   - Created {model_info['hidden_size'] * config.expansion_factor:,} SAE features")

print("\n✅ 4. Function Extraction:")
if 'extraction_result' in locals():
    print(f"   - Extracted behavioral function with {len(extraction_result.active_features)} active features")
    print(f"   - Achieved extraction strength: {extraction_result.extraction_strength:.6f}")
else:
    print("   - Function extraction framework demonstrated")

print("\n✅ 5. Representation Engineering:")
print("   - Created steering vectors for behavior modification")
print("   - Tested intervention effects on model outputs")
print("   - Demonstrated context vs knowledge steering")

print("\n✅ 6. Comprehensive Evaluation:")
if comprehensive_results:
    print(f"   - Best architecture: {best_arch.upper()}")
    print(f"   - Best score: {comprehensive_results[best_arch].overall_score:.4f}")
    print("   - Compared reconstruction quality, sparsity, and feature metrics")

print("\n✅ 7. Visualization:")
print("   - Created activation distribution plots")
print("   - Visualized feature importance")
print("   - Generated architecture comparison charts")
print("   - Produced feature activation heatmaps")

print("\n📁 Generated Files:")
saved_files = [
    f"llama_3_1_8b_layer{target_layer}_gated_sae.pt",
    f"llama_3_1_8b_layer{target_layer}_standard_sae.pt",
    f"llama_3_1_8b_layer{target_layer}_jumprelu_sae.pt",
    "activation_distribution.png",
    "feature_importance.png",
    "sae_comparison.png",
    "feature_heatmap.png"
]

for file in saved_files:
    print(f"   - {file}")

print("\n🚀 Next Steps:")
print("   1. Experiment with different models (GPT, BERT, Mistral, etc.)")
print("   2. Try different datasets (code, scientific, domain-specific)")
print("   3. Explore different hook points (attention, MLP, residual)")
print("   4. Scale up training with more tokens and larger expansion factors")
print("   5. Implement custom SAE architectures")
print("   6. Apply to real-world representation engineering tasks")
print("   7. Contribute to the SpARE library development")

print("\n📚 Resources:")
print("   - SpARE Documentation: Check the docs/ folder")
print("   - Example Scripts: See demo.py for more examples")
print("   - Research Paper: https://arxiv.org/pdf/2410.15999")
print("   - GitHub Issues: Report bugs and request features")

print("\n🎯 Happy SAE Training with SpARE! 🎯")