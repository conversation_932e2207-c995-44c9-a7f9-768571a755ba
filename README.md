# ITAS: Instruction-Truth Activation Steering

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![PyTorch](https://img.shields.io/badge/PyTorch-2.0+-red.svg)](https://pytorch.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![PyPI version](https://img.shields.io/pypi/v/itas.svg)](https://pypi.org/project/itas/)

A comprehensive, production-ready library for training and analyzing Sparse Auto-encoders (SAEs) on neural network representations. ITAS provides universal support for any HuggingFace model and dataset, making it the most flexible and powerful SAE framework available for instruction-truth activation steering.

## 🚀 Key Features

### 🌐 Universal Compatibility
- **Any HuggingFace Model**: GPT, BERT, T5, LLaMA, Mistral, <PERSON>, and more
- **Any HuggingFace Dataset**: Automatic preprocessing and tokenization
- **Flash Attention Support**: Automatic compatibility detection and graceful fallback
- **Multi-GPU Training**: Distributed training support for large models

### 🏗️ Advanced SAE Architectures
- **Standard SAE**: Classic sparse autoencoder with ReLU activation
- **Gated SAE**: Improved reconstruction with gating mechanism
- **JumpReLU SAE**: Enhanced sparsity with jump connections
- **Extensible Design**: Easy to add new architectures

### 🔧 Production-Ready Features
- **Type-Safe Configuration**: Comprehensive configuration management with validation
- **Robust Error Handling**: Graceful fallbacks and detailed error messages
- **Comprehensive Logging**: Built-in monitoring and progress tracking with W&B support
- **Memory Efficient**: Optimized for large-scale training with gradient checkpointing
- **Automatic Checkpointing**: Model saving and resuming capabilities

### 📊 Advanced Analysis Tools
- **Function Extraction**: Extract and analyze learned feature representations
- **Feature Importance Analysis**: Identify which features are most important
- **Activation Analysis**: Deep dive into model activations and patterns
- **Representation Engineering**: Create steering vectors for behavior modification
- **Comprehensive Evaluation**: Multi-metric SAE quality assessment
- **Visualization Tools**: Built-in plotting and analysis utilities

## 📦 Installation

### Quick Install from PyPI
```bash
# Install the latest stable version
pip install itas
```

### Install from Source
```bash
# Install from source (recommended for latest features)
git clone https://github.com/junkim100/itas.git
cd itas
pip install -e .
```

### Environment Setup
```bash
# Create conda environment
conda create -n itas python=3.9 -y
conda activate itas

# Install with all dependencies
pip install itas[dev]
```

### Requirements
- Python 3.8+
- PyTorch 2.0+
- Transformers 4.30+
- Datasets 2.0+
- Flash Attention 2 (optional, for performance)

## 🎯 Quick Start

### 30-Second Example
```python
import itas

# Train SAE on any model with any dataset
config = itas.SAEConfig(
    model=itas.ModelConfig(model_name="meta-llama/Llama-3.1-8B-Instruct"),
    dataset=itas.DatasetConfig(
        dataset_name="wikitext",
        dataset_kwargs={"name": "wikitext-2-raw-v1"}
    ),
    training=itas.TrainingConfig(total_training_tokens=10_000_000),
    hook_layer=16,  # Middle layer for LLaMA 3.1 8B
    expansion_factor=32,
)

# Train and save
trainer = itas.SAETrainer(config)
sae = trainer.train()
sae.save("llama_3_1_8b_sae.pt")
```

### 📚 Complete Tutorial
For a comprehensive end-to-end tutorial using LLaMA 3.1 8B Instruct, see our [**Interactive Tutorial Notebook**](tutorial.ipynb) which covers:
- Model setup and configuration
- Dataset preparation and preprocessing
- SAE training with different architectures
- Function extraction and analysis
- Representation engineering applications
- Evaluation and visualization

## 🚀 Library Overview

ITAS has been comprehensively designed to support any HuggingFace model and dataset, following software engineering best practices.

### ✨ Key Improvements

#### 1. Universal Model Support
- **Before**: Limited to LLaMA and Gemma models with hardcoded configurations
- **After**: Works with any HuggingFace transformer model (GPT, BERT, T5, OPT, etc.)
- **Implementation**: `UniversalModelLoader` with automatic architecture detection

#### 2. Flexible Dataset Handling
- **Before**: Hardcoded dataset paths and preprocessing
- **After**: Support for any HuggingFace dataset with configurable preprocessing
- **Implementation**: `DatasetManager` with streaming and chunking support

#### 3. Multiple SAE Architectures
- **Before**: Single SAE implementation
- **After**: Standard, Gated, and JumpReLU SAE variants
- **Implementation**: Unified `SAE` class with architecture parameter

#### 4. Comprehensive Configuration
- **Before**: Scattered configuration across multiple files
- **After**: Hierarchical configuration system with validation
- **Implementation**: `SAEConfig` with nested configuration classes

#### 5. Professional Software Engineering
- **Before**: Limited error handling and documentation
- **After**: Type hints, logging, error handling, and comprehensive documentation
- **Implementation**: Following Python best practices throughout

## 🏗️ Architecture

### Core Module (`itas.core`)

```python
# Configuration Management
from itas.core.config import SAEConfig, ModelConfig, DatasetConfig, TrainingConfig

# Model Loading
from itas.core.model_loader import UniversalModelLoader

# Dataset Handling
from itas.core.dataset_manager import DatasetManager

# SAE Implementation
from itas.core.sae import SAE, TrainingSAE

# Training
from itas.core.trainer import SAETrainer

# Activation Management
from itas.core.activations_store import ActivationsStore
```

### Analysis Module (`itas.analysis`)

```python
# Function Extraction
from itas.analysis.function_extractor import FunctionExtractor

# Representation Engineering
from itas.analysis.representation_engineer import RepresentationEngineer

# Activation Analysis
from itas.analysis.activation_analyzer import ActivationAnalyzer

# Evaluation
from itas.analysis.evaluation import SAEEvaluator

# Visualization
from itas.analysis.visualization import SAEVisualizer
```

## Project Setup

```bash
conda create -n itas python=3.9 -y
conda activate itas
pip install itas
```

Or install from source:
```bash
git clone https://github.com/junkim100/itas.git
cd itas
pip install -e .
```

## 🔧 Comprehensive Usage Guide

### 1. Quick Demo
```bash
# Run the comprehensive demo showcasing all features
python demo.py
```

### 2. Basic SAE Training

```python
import itas

# Simple configuration for any HuggingFace model
config = itas.SAEConfig(
    model=itas.ModelConfig(
        model_name="meta-llama/Llama-3.1-8B-Instruct",
        use_flash_attention=True,
        torch_dtype="bfloat16"
    ),
    dataset=itas.DatasetConfig(
        dataset_name="wikitext",
        dataset_kwargs={"name": "wikitext-2-raw-v1"},
        text_column="text"
    ),
    training=itas.TrainingConfig(
        total_training_tokens=50_000_000,
        batch_size=4096,
        l1_coefficient=1e-3,
        use_wandb=True,
        wandb_project="itas-llama-experiments"
    ),
    hook_layer=16,  # Middle layer for 32-layer model
    expansion_factor=32,
    architecture="gated"  # Use gated SAE for better performance
)

# Train SAE with automatic checkpointing
trainer = itas.SAETrainer(config)
sae = trainer.train()

# Save the trained SAE
sae.save("llama_3_1_8b_layer16_gated_sae.pt")
```

### 3. Advanced Configuration

```python
# Detailed configuration with all options
config = itas.SAEConfig(
    # Model configuration
    model=itas.ModelConfig(
        model_name="meta-llama/Llama-3.1-8B-Instruct",
        use_flash_attention=True,  # Automatic compatibility detection
        torch_dtype="bfloat16",
        trust_remote_code=False,
        device_map="auto",  # Automatic device placement
    ),

    # Dataset configuration
    dataset=itas.DatasetConfig(
        dataset_name="wikitext",
        dataset_kwargs={"name": "wikitext-2-raw-v1"},
        dataset_split="train",
        text_column="text",
        max_seq_length=2048,
        chunk_size=2048,
        streaming=False,
        num_proc=8,  # Parallel processing
        trust_remote_code=False,
    ),

    # Training configuration
    training=itas.TrainingConfig(
        total_training_tokens=100_000_000,
        batch_size=4096,
        learning_rate=3e-4,
        l1_coefficient=1e-3,
        lr_scheduler="cosine",
        lr_warm_up_steps=1000,
        adam_beta1=0.9,
        adam_beta2=0.999,
        weight_decay=0.01,

        # Checkpointing
        checkpoint_every_n_tokens=5_000_000,
        save_checkpoint_dir="./checkpoints",

        # Logging and evaluation
        log_every_n_steps=100,
        eval_every_n_tokens=10_000_000,
        use_wandb=True,
        wandb_project="itas-llama-experiments",
        wandb_entity="your-team",
    ),

    # SAE architecture
    architecture="gated",  # "standard", "gated", or "jumprelu"
    expansion_factor=32,
    hook_layer=16,
    hook_name="model.layers.{layer}.mlp",  # Hook into MLP
    activation_fn="relu",
    normalize_decoder=True,

    # Device and precision
    device="cuda",
    dtype="float32",
    seed=42,
)
```

### 4. Universal Model Loading

```python
# Load any HuggingFace model with automatic configuration
model, tokenizer = itas.load_model_and_tokenizer(
    "meta-llama/Llama-3.1-8B-Instruct",
    use_flash_attention=True,
    torch_dtype="bfloat16"
)

# Get detailed model information
model_loader = itas.UniversalModelLoader(
    itas.ModelConfig(model_name="meta-llama/Llama-3.1-8B-Instruct")
)
model_info = model_loader.get_model_info()
print(f"Model: {model_info['model_name']}")
print(f"Architecture: {model_info['architecture']}")
print(f"Hidden size: {model_info['hidden_size']}")
print(f"Layers: {model_info['num_layers']}")
print(f"Parameters: {model_info['total_parameters']:,}")

# Get available hook points
hook_names = model_loader.get_hook_names()
print(f"Available hooks: {list(hook_names.keys())}")
```

### 5. Flexible Dataset Processing

```python
# Use any HuggingFace dataset with custom preprocessing
dataset_config = itas.DatasetConfig(
    dataset_name="EleutherAI/pile",  # Large dataset
    dataset_split="train",
    text_column="text",
    max_seq_length=2048,
    chunk_size=2048,
    streaming=True,  # Essential for large datasets
    num_proc=16,  # Parallel processing
    trust_remote_code=True,  # If required by dataset
)

# Initialize dataset manager
dataset_manager = itas.DatasetManager(dataset_config, tokenizer)

# Load and preprocess dataset
dataset = dataset_manager.load_dataset()
processed_dataset = dataset_manager.preprocess_dataset()

# Get dataset statistics
dataset_info = dataset_manager.get_dataset_info()
print(f"Dataset: {dataset_info['dataset_name']}")
print(f"Raw size: {dataset_info['raw_size']:,}")
print(f"Processed size: {dataset_info['processed_size']:,}")
```

### 6. Advanced Function Extraction

```python
# Load a pre-trained SAE
sae = itas.SAE.load("llama_3_1_8b_layer16_gated_sae.pt")

# Create function extractor
function_extractor = itas.FunctionExtractor(
    sae=sae,
    initialization_method="uniform",
    regularization_strength=1e-5,
    device="cuda"
)

# Prepare activation data
# positive_examples: activations when model exhibits desired behavior
# negative_examples: activations when model exhibits undesired behavior
positive_activations = get_activations_for_behavior(model, positive_prompts)
negative_activations = get_activations_for_behavior(model, negative_prompts)

# Extract behavioral function
result = function_extractor.extract_function(
    target_activations=positive_activations,
    context_activations=negative_activations,
    learning_rate=1e-3,
    num_iterations=1000,
    verbose=True
)

print(f"Extracted function with {len(result.active_features)} active features")
print(f"Extraction strength: {result.extraction_strength:.6f}")
print(f"Final loss: {result.metadata['final_loss']:.6f}")

# Analyze feature importance
importance_stats = function_extractor.analyze_feature_importance()
print(f"Feature importance stats: {importance_stats}")

# Get top contributing features
top_features = function_extractor.get_top_features(k=20)
print(f"Top 20 features: {top_features}")
```

### 7. Representation Engineering

```python
# Create representation engineer for behavior modification
engineer = itas.RepresentationEngineer(
    model=model,
    tokenizer=tokenizer,
    sae=sae,
    hook_layer=16,
    hook_name="model.layers.16.mlp"
)

# Define examples for steering vector creation
positive_examples = [
    "Based on the context provided, the answer is...",
    "According to the given information...",
    "The context clearly states that..."
]

negative_examples = [
    "From my knowledge, I believe...",
    "Based on what I know...",
    "Generally speaking..."
]

# Create steering vector for context-based vs knowledge-based responses
steering_vector = engineer.create_steering_vector(
    positive_examples=positive_examples,
    negative_examples=negative_examples,
    strength=2.0
)

# Apply intervention during generation
intervention_fn = engineer.apply_steering_intervention(
    steering_vector,
    strength=1.5,
    layers=[16]  # Apply to specific layers
)

# Test intervention effectiveness
test_prompts = [
    "What is the capital of France?",
    "Explain quantum computing.",
    "How does photosynthesis work?"
]

results = engineer.test_intervention(
    test_prompts,
    intervention_fn,
    max_new_tokens=100,
    temperature=0.7
)

for prompt, result in zip(test_prompts, results):
    print(f"Prompt: {prompt}")
    print(f"Modified response: {result['modified_text']}")
    print(f"Original response: {result['original_text']}")
    print("---")
```

### 8. Comprehensive SAE Evaluation

```python
# Create evaluator for comprehensive SAE assessment
evaluator = itas.SAEEvaluator()

# Prepare test data
test_activations = get_test_activations(model, test_dataset)

# Comprehensive evaluation
evaluation = evaluator.evaluate_sae_comprehensive(
    sae=sae,
    test_activations=test_activations,
    compute_feature_metrics=True,
    compute_reconstruction_metrics=True,
    compute_sparsity_metrics=True
)

print(f"Overall SAE Score: {evaluation.overall_score:.4f}")
print(f"Reconstruction Quality:")
print(f"  - FVU (Fraction of Variance Unexplained): {evaluation.reconstruction_metrics['fvu']:.4f}")
print(f"  - MSE: {evaluation.reconstruction_metrics['mse']:.6f}")
print(f"  - Cosine Similarity: {evaluation.reconstruction_metrics['cosine_sim']:.4f}")

print(f"Sparsity Metrics:")
print(f"  - Overall Sparsity: {evaluation.sparsity_metrics['overall_sparsity']:.4f}")
print(f"  - Dead Features: {evaluation.sparsity_metrics['dead_features']}")
print(f"  - Active Features: {evaluation.sparsity_metrics['active_features']}")

print(f"Feature Quality:")
print(f"  - Feature Diversity: {evaluation.feature_metrics['diversity']:.4f}")
print(f"  - Feature Stability: {evaluation.feature_metrics['stability']:.4f}")

# Compare multiple SAE architectures
sae_standard = itas.SAE.load("llama_3_1_8b_standard_sae.pt")
sae_gated = itas.SAE.load("llama_3_1_8b_gated_sae.pt")
sae_jumprelu = itas.SAE.load("llama_3_1_8b_jumprelu_sae.pt")

comparison = evaluator.compare_saes(
    saes=[sae_standard, sae_gated, sae_jumprelu],
    sae_names=["Standard", "Gated", "JumpReLU"],
    test_data=test_activations
)

print("SAE Architecture Comparison:")
for name, metrics in comparison.items():
    print(f"{name}: Score={metrics['overall_score']:.4f}, "
          f"FVU={metrics['fvu']:.4f}, "
          f"Sparsity={metrics['sparsity']:.4f}")
```

### 9. Visualization and Analysis

```python
# Create comprehensive visualizations
visualizer = spare.SAEVisualizer()

# Plot activation distributions
fig1 = visualizer.plot_activation_distribution(
    activations=test_activations,
    title="LLaMA 3.1 8B Layer 16 Activations",
    save_path="activation_dist.png"
)

# Plot feature importance from function extraction
fig2 = visualizer.plot_feature_importance(
    importance_scores=result.feature_weights,
    top_k=50,
    title="Top 50 Features for Context vs Knowledge Behavior",
    save_path="feature_importance.png"
)

# Plot training metrics
training_metrics = trainer.get_training_metrics()
fig3 = visualizer.plot_training_metrics(
    metrics=training_metrics,
    title="SAE Training Progress",
    save_path="training_progress.png"
)

# Plot SAE comparison
fig4 = visualizer.plot_sae_comparison(
    comparison_results=comparison,
    metrics=["overall_score", "fvu", "sparsity"],
    title="SAE Architecture Comparison",
    save_path="sae_comparison.png"
)

# Interactive feature exploration
visualizer.create_interactive_feature_explorer(
    sae=sae,
    activations=test_activations,
    save_path="feature_explorer.html"
)
```

## 📊 Supported Models and Datasets

### 🤖 Models (Universal HuggingFace Support)

SpARE supports **any** HuggingFace transformer model with automatic architecture detection:

#### **Large Language Models**
- **LLaMA Family**: LLaMA, LLaMA 2, LLaMA 3, LLaMA 3.1, Code Llama
- **GPT Family**: GPT-2, GPT-3.5, GPT-4, DialoGPT, CodeGPT
- **Mistral Family**: Mistral 7B, Mixtral 8x7B, Mistral Instruct
- **Gemma Family**: Gemma 2B, Gemma 7B, CodeGemma
- **OPT Family**: OPT-125M to OPT-175B
- **Falcon Family**: Falcon-7B, Falcon-40B, Falcon-180B

#### **Encoder Models**
- **BERT Family**: BERT, RoBERTa, DistilBERT, DeBERTa
- **Specialized**: SciBERT, BioBERT, FinBERT, LegalBERT

#### **Encoder-Decoder Models**
- **T5 Family**: T5, Flan-T5, UL2, mT5
- **BART Family**: BART, DistilBART

#### **Other Architectures**
- **MPT**: MPT-7B, MPT-30B
- **Bloom**: BLOOM-560M to BLOOM-176B
- **Custom Models**: Any model following HuggingFace conventions

### 📚 Datasets (Universal HuggingFace Support)

SpARE works with **any** HuggingFace dataset with automatic preprocessing:

#### **General Text Datasets**
- **WikiText**: wikitext-2, wikitext-103
- **OpenWebText**: openwebtext, openwebtext2
- **C4**: Common Crawl cleaned
- **The Pile**: EleutherAI's diverse text dataset
- **BookCorpus**: Books dataset

#### **Code Datasets**
- **The Stack**: Multi-language code dataset
- **CodeParrot**: Python code dataset
- **GitHub Code**: Various programming languages

#### **Instruction/Chat Datasets**
- **Alpaca**: Instruction following dataset
- **Vicuna**: Conversation dataset
- **ShareGPT**: Shared conversations
- **Dolly**: Databricks instruction dataset

#### **Domain-Specific Datasets**
- **Scientific**: ArXiv, PubMed, S2ORC
- **Legal**: Legal documents and cases
- **Medical**: Medical literature and records

#### **Custom Datasets**
- Any dataset with text columns
- Streaming support for large datasets
- Custom preprocessing pipelines

## 🔬 Research Applications

### 🔍 Mechanistic Interpretability
- **Feature Discovery**: Identify interpretable features in neural networks
- **Circuit Analysis**: Understand how features compose into circuits
- **Activation Patching**: Causal intervention experiments
- **Representation Analysis**: Deep dive into internal representations

### 🎯 Representation Engineering
- **Behavior Steering**: Control model outputs and behaviors
- **Bias Mitigation**: Reduce harmful biases in model outputs
- **Safety Alignment**: Improve model safety and alignment
- **Knowledge Editing**: Modify specific knowledge in models

### 📈 Model Analysis
- **Comparative Studies**: Compare representations across models
- **Architecture Analysis**: Understand different model architectures
- **Training Dynamics**: Study how representations change during training
- **Transfer Learning**: Analyze feature transfer between tasks

## 🔄 Migration Guide

### From Legacy Code

The refactored library maintains backward compatibility:

```python
# Legacy (still works but deprecated)
model, tokenizer = spare.load_model(model_path, flash_attn=True)
sae = spare.load_frozen_sae(layer_idx, model_name)

# New recommended approach
model, tokenizer = spare.load_model_and_tokenizer(
    model_name=model_path,
    use_flash_attention=True
)

config = spare.SAEConfig(model_name=model_path, hook_layer=layer_idx)
trainer = spare.SAETrainer(config)
sae = trainer.train()
```

### Configuration Migration

```python
# Old: Scattered configuration
model_path = "meta-llama/Llama-2-7b-hf"
layer_ids = [12, 13, 14, 15]
batch_size = 4096
l1_coeff = 1e-3

# New: Unified configuration
config = spare.SAEConfig(
    model=spare.ModelConfig(model_name="meta-llama/Llama-2-7b-hf"),
    training=spare.TrainingConfig(
        batch_size=4096,
        l1_coefficient=1e-3
    ),
    hook_layer=12
)
```

## 🎯 Benefits

1. **Generalizability**: Works with any transformer architecture
2. **Flexibility**: Configurable for different use cases
3. **Scalability**: Handles large models and datasets efficiently
4. **Maintainability**: Clean, modular code with proper documentation
5. **Extensibility**: Easy to add new SAE architectures and features
6. **Reliability**: Comprehensive error handling and validation
7. **Usability**: Simple API with sensible defaults

## 🧪 Research Experiments

This section describes how to reproduce the experiments from the original paper.

### Run All Experiments

Use the cached intermediate data to run experiments.

The cached data is in the `cache_data` folder, including mutual information, expectation, and the values of functional SAE activations.

```bash
bash ./scripts/run_all_experiments.sh
```

### Run SpARE Step by Step

Observe the outputs of prompts and group them based on the knowledge selection behaviours:
```bash
bash ./scripts/run_group_prompts.sh
```

Save the activations of grouped prompts:
```bash
bash ./scripts/run_save_grouped_activations.sh
```

Estimate the mutual information and expectations for each SAE activation:

```bash
bash ./scripts/run_mutual_information_and_expectations.sh
```

Evaluate SpARE
```bash
python ./scripts/run_spare.py \
  --model_path="meta-llama/Llama-2-7b-hf" \
  --data_name="nqswap" \
  --layer_ids 12 13 14 15 \
  --edit_degree=2.0 \
  --select_topk_proportion=0.07 \
  --seed=42 \
  --hiddens_name="grouped_activations" \
  --mutual_information_save_name="mutual_information" \
  --run_use_parameter \
  --run_use_context
```

## 🚀 Getting Started with the Refactored Library

1. **Install the library**:
   ```bash
   pip install -e .
   ```

2. **Run the demo**:
   ```bash
   python demo.py
   ```

3. **Try different models**:
   ```python
   # Test with different architectures
   models = ["microsoft/DialoGPT-medium", "distilbert-base-uncased", "facebook/opt-125m"]
   for model_name in models:
       config = spare.SAEConfig(model_name=model_name)
       # ... train SAE
   ```

4. **Explore the documentation**:
   - Check docstrings in each module
   - Review the demo.py for comprehensive examples
   - Look at the configuration options in `spare.core.config`

## 📝 Current Status

**Note**: The original SpARE implementation currently supports short-form ODQA tasks. The refactored library provides a foundation for extending to more tasks and model architectures. We plan to add support for more tasks in future versions.

The refactored library provides a solid foundation for:
- Adding new SAE architectures
- Supporting additional model types
- Implementing new intervention techniques
- Scaling to larger models and datasets
- Building applications on top of the library

This refactoring transforms SpARE from a research prototype into a production-ready library suitable for widespread use in the representation engineering community.

## Acknowledgement

The implementation of the sparse auto-encoder is adapted from EleutherAI/sae https://github.com/EleutherAI/sae and jbloomAus/SAELens https://github.com/jbloomAus/SAELens.
We appreciate their open-source contributions!

## Citing

Steering Knowledge Selection Behaviours in LLMs via SAE-Based Representation Engineering
```text
@misc{zhao2024steeringknowledgeselectionbehaviours,
      title={Steering Knowledge Selection Behaviours in LLMs via SAE-Based Representation Engineering},
      author={Yu Zhao and Alessio Devoto and Giwon Hong and Xiaotang Du and Aryo Pradipta Gema and Hongru Wang and Xuanli He and Kam-Fai Wong and Pasquale Minervini},
      year={2024},
      eprint={2410.15999},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2410.15999},
}
```

The preliminary study: Analysing the Residual Stream of Language Models Under Knowledge Conflicts

```text
@misc{zhao2024analysingresidualstreamlanguage,
      title={Analysing the Residual Stream of Language Models Under Knowledge Conflicts},
      author={Yu Zhao and Xiaotang Du and Giwon Hong and Aryo Pradipta Gema and Alessio Devoto and Hongru Wang and Xuanli He and Kam-Fai Wong and Pasquale Minervini},
      year={2024},
      eprint={2410.16090},
      archivePrefix={arXiv},
      primaryClass={cs.CL},
      url={https://arxiv.org/abs/2410.16090},
}
```